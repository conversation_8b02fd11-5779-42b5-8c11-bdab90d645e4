#!/bin/bash

# Hysteria 2 + SOCKS5 交互式管理脚本
# 不影响sing-box服务，独立管理Hysteria 2服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
HYSTERIA_CONFIG="/etc/hysteria/config.yaml"
HYSTERIA_CERT="/etc/hysteria/cert.pem"
HYSTERIA_KEY="/etc/hysteria/private.key"
SERVICE_NAME="hysteria-server"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 显示标题
show_header() {
    clear
    echo -e "${CYAN}================================================${NC}"
    echo -e "${CYAN}    Hysteria 2 + SOCKS5 交互式管理工具${NC}"
    echo -e "${CYAN}================================================${NC}"
    echo ""
}

# 显示菜单
show_menu() {
    echo -e "${PURPLE}请选择操作:${NC}"
    echo -e "${YELLOW}1.${NC} 取消/杀死现有的所有 Hysteria 2 服务"
    echo -e "${YELLOW}2.${NC} 开启一个 Hysteria 2 + SOCKS5 服务"
    echo -e "${YELLOW}3.${NC} 显示现有 Hysteria 2 + SOCKS5 服务配置"
    echo -e "${YELLOW}0.${NC} 退出"
    echo ""
    echo -n -e "${CYAN}请输入选项 [0-3]: ${NC}"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 获取服务器IP
get_server_ip() {
    local ip=$(curl -s --max-time 10 ifconfig.me 2>/dev/null || curl -s --max-time 10 ipinfo.io/ip 2>/dev/null || curl -s --max-time 10 icanhazip.com 2>/dev/null)
    if [[ -z "$ip" ]]; then
        log_error "无法获取服务器IP地址"
        exit 1
    fi
    echo "$ip"
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if netstat -tulpn 2>/dev/null | grep -q ":$port "; then
        return 1
    fi
    return 0
}

# 显示已占用的端口
show_occupied_ports() {
    echo -e "${BLUE}正在检查已占用的端口...${NC}" >&2
    echo "" >&2

    # 检查sing-box占用的端口
    local singbox_ports=$(netstat -tulpn 2>/dev/null | grep -E "sing-box|hysteria" | awk '{print $4}' | cut -d: -f2 | sort -u | tr '\n' ' ')
    if [[ -n "$singbox_ports" ]]; then
        echo -e "${YELLOW}Sing-box/Hysteria 相关占用端口:${NC} $singbox_ports" >&2
    fi

    # 检查常用端口
    local common_ports=$(netstat -tulpn 2>/dev/null | grep -E ":(80|443|22|8080|8443|9090)" | awk '{print $4}' | cut -d: -f2 | sort -u | tr '\n' ' ')
    if [[ -n "$common_ports" ]]; then
        echo -e "${YELLOW}常用端口占用:${NC} $common_ports" >&2
    fi

    echo "" >&2
}

# 选择端口
select_port() {
    show_occupied_ports

    while true; do
        echo -n -e "${CYAN}请输入要使用的端口 (建议范围: 8000-9999): ${NC}" >&2
        read -r port

        if [[ ! "$port" =~ ^[0-9]+$ ]]; then
            echo -e "${RED}[ERROR]${NC} 请输入有效的端口号" >&2
            continue
        fi

        if [[ $port -lt 1024 || $port -gt 65535 ]]; then
            echo -e "${RED}[ERROR]${NC} 端口范围应在 1024-65535 之间" >&2
            continue
        fi

        if check_port $port; then
            echo -e "${GREEN}[INFO]${NC} 端口 $port 可用" >&2
            echo "$port"
            return 0
        else
            echo -e "${YELLOW}[WARN]${NC} 端口 $port 已被占用，请选择其他端口" >&2
        fi
    done
}

# 输入SOCKS5配置
input_socks5_config() {
    echo -e "${PURPLE}请输入SOCKS5代理配置:${NC}" >&2
    echo "" >&2

    # 输入主机地址和端口
    while true; do
        echo -n -e "${CYAN}SOCKS5主机地址:端口 (例: us.cliproxy.io:3010): ${NC}" >&2
        read -r socks5_addr

        if [[ "$socks5_addr" =~ ^[a-zA-Z0-9.-]+:[0-9]+$ ]]; then
            local host=$(echo "$socks5_addr" | cut -d: -f1)
            local port=$(echo "$socks5_addr" | cut -d: -f2)
            break
        else
            echo -e "${RED}[ERROR]${NC} 格式错误，请使用 主机:端口 格式" >&2
        fi
    done

    # 输入用户名
    echo -n -e "${CYAN}SOCKS5用户名: ${NC}" >&2
    read -r socks5_user

    # 输入密码
    echo -n -e "${CYAN}SOCKS5密码: ${NC}" >&2
    read -r socks5_pass

    echo "$socks5_addr:$socks5_user:$socks5_pass"
}

# 生成随机密码
generate_password() {
    openssl rand -hex 16
}

# 生成自签名证书
generate_certificates() {
    log_info "正在生成TLS证书..."
    
    mkdir -p /etc/hysteria
    
    # 生成RSA私钥
    openssl genrsa -out "$HYSTERIA_KEY" 2048 2>/dev/null
    
    # 生成自签名证书
    openssl req -new -x509 -key "$HYSTERIA_KEY" -out "$HYSTERIA_CERT" -days 365 -subj "/CN=bing.com" 2>/dev/null
    
    # 设置权限
    chmod 644 "$HYSTERIA_CERT"
    chmod 644 "$HYSTERIA_KEY"
    
    # 验证证书文件
    if [[ -f "$HYSTERIA_CERT" && -f "$HYSTERIA_KEY" ]]; then
        local cert_size=$(stat -c%s "$HYSTERIA_CERT")
        local key_size=$(stat -c%s "$HYSTERIA_KEY")
        if [[ $cert_size -gt 100 && $key_size -gt 100 ]]; then
            log_info "TLS证书生成完成"
            return 0
        fi
    fi
    
    log_error "TLS证书生成失败"
    return 1
}

# 生成Hysteria 2配置文件
generate_hysteria2_config() {
    local port=$1
    local password=$2
    local proxy_host=$3
    local proxy_port=$4
    local proxy_user=$5
    local proxy_pass=$6
    
    log_info "正在生成Hysteria 2配置文件..."
    
    cat > "$HYSTERIA_CONFIG" << EOF
listen: :$port

tls:
  cert: $HYSTERIA_CERT
  key: $HYSTERIA_KEY

auth:
  type: password
  password: $password

masquerade:
  type: proxy
  proxy:
    url: https://www.bing.com
    rewriteHost: true

quic:
  initStreamReceiveWindow: 8388608
  maxStreamReceiveWindow: 8388608
  initConnReceiveWindow: 20971520
  maxConnReceiveWindow: 20971520
  maxIdleTimeout: 30s
  maxIncomingStreams: 1024
  disablePathMTUDiscovery: false

bandwidth:
  up: 1 gbps
  down: 1 gbps

ignoreClientBandwidth: false
disableUDP: false
udpIdleTimeout: 60s

resolver:
  type: udp
  udp:
    addr: *******:53
    timeout: 4s

outbounds:
  - name: socks5proxy
    type: socks5
    socks5:
      addr: $proxy_host:$proxy_port
      username: $proxy_user
      password: $proxy_pass
  - name: direct
    type: direct

acl:
  inline:
    - "socks5proxy(all)"
EOF

    log_info "Hysteria 2配置文件生成完成"
}

# 创建systemd服务
create_systemd_service() {
    log_info "正在创建systemd服务..."

    cat > /etc/systemd/system/$SERVICE_NAME.service << EOF
[Unit]
Description=Hysteria Server Service (config.yaml)
After=network.target nss-lookup.target

[Service]
Type=simple
StandardError=journal
User=root
AmbientCapabilities=CAP_NET_BIND_SERVICE
ExecStart=/usr/local/bin/hysteria server --config $HYSTERIA_CONFIG
ExecReload=/bin/kill -HUP \$MAINPID
Restart=on-failure
RestartSec=10
RestartPreventExitStatus=23

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    systemctl enable $SERVICE_NAME
    log_info "systemd服务创建完成"
}

# 启动服务
start_service() {
    log_info "正在启动Hysteria 2服务..."

    systemctl stop $SERVICE_NAME 2>/dev/null || true
    sleep 2
    systemctl start $SERVICE_NAME

    sleep 3

    if systemctl is-active --quiet $SERVICE_NAME; then
        log_info "Hysteria 2服务启动成功"
        return 0
    else
        log_error "Hysteria 2服务启动失败"
        journalctl -u $SERVICE_NAME --no-pager -n 10
        return 1
    fi
}

# 停止所有Hysteria 2服务
stop_all_hysteria_services() {
    echo -e "${YELLOW}正在停止所有Hysteria 2服务...${NC}"
    echo ""

    # 停止systemd服务
    if systemctl is-active --quiet $SERVICE_NAME 2>/dev/null; then
        log_info "停止 $SERVICE_NAME 服务..."
        systemctl stop $SERVICE_NAME
        systemctl disable $SERVICE_NAME 2>/dev/null || true
    fi

    # 查找并杀死所有hysteria进程（排除sing-box相关）
    local hysteria_pids=$(pgrep -f "hysteria.*server.*config" 2>/dev/null | grep -v sing-box || true)
    if [[ -n "$hysteria_pids" ]]; then
        log_info "发现独立的Hysteria进程，正在终止..."
        echo "$hysteria_pids" | xargs kill -TERM 2>/dev/null || true
        sleep 2
        echo "$hysteria_pids" | xargs kill -KILL 2>/dev/null || true
    fi

    # 删除服务文件
    if [[ -f "/etc/systemd/system/$SERVICE_NAME.service" ]]; then
        log_info "删除systemd服务文件..."
        rm -f "/etc/systemd/system/$SERVICE_NAME.service"
        systemctl daemon-reload
    fi

    log_info "所有独立的Hysteria 2服务已停止（sing-box服务未受影响）"
    echo ""
    echo -n -e "${CYAN}按回车键继续...${NC}"
    read
}

# 安装Hysteria 2 + SOCKS5服务
install_hysteria2_socks5() {
    echo -e "${YELLOW}开始安装 Hysteria 2 + SOCKS5 服务...${NC}"
    echo ""

    # 检查Hysteria 2是否已安装
    if ! command -v /usr/local/bin/hysteria >/dev/null 2>&1; then
        log_error "Hysteria 2未安装，请先安装Hysteria 2"
        echo -n -e "${CYAN}按回车键继续...${NC}"
        read
        return 1
    fi

    # 获取服务器IP
    local server_ip=$(get_server_ip)
    log_info "服务器IP: $server_ip"

    # 选择端口
    local port=$(select_port)

    # 输入SOCKS5配置
    local socks5_config=$(input_socks5_config)
    IFS=':' read -r proxy_host proxy_port proxy_user proxy_pass <<< "$socks5_config"

    # 生成密码
    local password=$(generate_password)
    log_info "生成的Hysteria 2密码: $password"

    echo ""
    echo -e "${PURPLE}配置摘要:${NC}"
    echo -e "${CYAN}服务器:${NC} $server_ip:$port"
    echo -e "${CYAN}密码:${NC} $password"
    echo -e "${CYAN}SOCKS5代理:${NC} $proxy_host:$proxy_port"
    echo -e "${CYAN}SOCKS5用户:${NC} $proxy_user"
    echo ""

    echo -n -e "${YELLOW}确认安装? [y/N]: ${NC}"
    read -r confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        log_warn "安装已取消"
        echo -n -e "${CYAN}按回车键继续...${NC}"
        read
        return 0
    fi

    # 生成证书
    if ! generate_certificates; then
        log_error "证书生成失败"
        echo -n -e "${CYAN}按回车键继续...${NC}"
        read
        return 1
    fi

    # 生成配置文件
    generate_hysteria2_config "$port" "$password" "$proxy_host" "$proxy_port" "$proxy_user" "$proxy_pass"

    # 创建服务
    create_systemd_service

    # 启动服务
    if start_service; then
        echo ""
        log_info "=== 安装成功！==="
        echo -e "${GREEN}服务器地址:${NC} $server_ip"
        echo -e "${GREEN}端口:${NC} $port"
        echo -e "${GREEN}密码:${NC} $password"
        echo -e "${GREEN}导入链接:${NC}"
        echo "hysteria2://$password@$server_ip:$port/?insecure=1#Hysteria2-SOCKS5-$server_ip"
    else
        log_error "服务启动失败"
    fi

    echo ""
    echo -n -e "${CYAN}按回车键继续...${NC}"
    read
}

# 显示当前配置
show_current_config() {
    echo -e "${YELLOW}当前 Hysteria 2 + SOCKS5 服务配置${NC}"
    echo ""

    # 检查服务状态
    if ! systemctl is-active --quiet $SERVICE_NAME 2>/dev/null; then
        log_warn "Hysteria 2服务未运行"
        echo -n -e "${CYAN}按回车键继续...${NC}"
        read
        return 1
    fi

    # 检查配置文件
    if [[ ! -f "$HYSTERIA_CONFIG" ]]; then
        log_error "配置文件不存在: $HYSTERIA_CONFIG"
        echo -n -e "${CYAN}按回车键继续...${NC}"
        read
        return 1
    fi

    # 获取服务器IP
    local server_ip=$(get_server_ip)

    # 从配置文件提取信息
    local port=$(grep "listen:" "$HYSTERIA_CONFIG" | awk '{print $2}' | sed 's/://')
    local password=$(grep "password:" "$HYSTERIA_CONFIG" | head -1 | awk '{print $2}')
    local socks5_addr=$(grep "addr:" "$HYSTERIA_CONFIG" | grep -v "*******" | awk '{print $2}')
    local socks5_user=$(grep "username:" "$HYSTERIA_CONFIG" | awk '{print $2}')

    # 显示配置信息
    echo -e "${GREEN}=== Hysteria 2 + SOCKS5 服务配置 ===${NC}"
    echo ""
    echo -e "${CYAN}服务状态:${NC} ${GREEN}运行中${NC}"
    echo -e "${CYAN}服务器地址:${NC} $server_ip"
    echo -e "${CYAN}端口:${NC} $port"
    echo -e "${CYAN}密码:${NC} $password"
    echo -e "${CYAN}协议:${NC} Hysteria 2"
    echo -e "${CYAN}跳过证书验证:${NC} 是"
    echo -e "${CYAN}SOCKS5代理:${NC} $socks5_addr"
    echo -e "${CYAN}SOCKS5用户:${NC} $socks5_user"
    echo ""

    # 生成导入链接
    local hysteria_link="hysteria2://$password@$server_ip:$port/?insecure=1#Hysteria2-SOCKS5-$server_ip"
    echo -e "${BLUE}导入链接:${NC}"
    echo "$hysteria_link"
    echo ""

    # 生成二维码
    if command -v qrencode >/dev/null 2>&1; then
        echo -e "${BLUE}二维码:${NC}"
        qrencode -t ANSIUTF8 "$hysteria_link"
        echo ""
    else
        log_warn "qrencode未安装，无法生成二维码"
        echo ""
    fi

    # 显示服务详细状态
    echo -e "${BLUE}服务详细状态:${NC}"
    systemctl status $SERVICE_NAME --no-pager -l

    echo ""
    echo -n -e "${CYAN}按回车键继续...${NC}"
    read
}

# 主函数
main() {
    check_root

    while true; do
        show_header
        show_menu

        read -r choice
        echo ""

        case $choice in
            1)
                stop_all_hysteria_services
                ;;
            2)
                install_hysteria2_socks5
                ;;
            3)
                show_current_config
                ;;
            0)
                echo -e "${GREEN}感谢使用 Hysteria 2 管理工具！${NC}"
                exit 0
                ;;
            *)
                log_error "无效选项，请重新选择"
                echo -n -e "${CYAN}按回车键继续...${NC}"
                read
                ;;
        esac
    done
}

# 运行主函数
main "$@"
